#!/usr/bin/env python3
"""Script to create test files for the preeti unicode converter."""

import os
from docx import Document
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_test_directory():
    """Create test_files directory if it doesn't exist."""
    os.makedirs("test_files", exist_ok=True)

def create_docx_file():
    """Create a sample DOCX file with Preeti text."""
    doc = Document()
    
    # Add title
    title = doc.add_heading('g]kfnL efiff k|]tL km06 6]S;6', 0)
    
    # Add paragraphs with Preeti text
    doc.add_paragraph(
        'of] Pp6f g]kfnL efiff k|]tL km06df n]lvPsf] b:tfj]h xf] . '
        'o;df ljleGg k|sf/sf g]kfnL zAbx? / jfSox? 5g\\ .'
    )
    
    doc.add_heading('ljz]iftfx?', level=1)
    doc.add_paragraph('• PDF kmfOnx? kl/jt{g')
    doc.add_paragraph('• Word b:tfj]hx? kl/jt{g')
    doc.add_paragraph('• ;fdfGo 6]S;6 kmfOnx? kl/jt{g')
    doc.add_paragraph('• Batch k|f];];Ë ;'ljwf')
    
    doc.add_heading('k|of]u ljlw', level=1)
    doc.add_paragraph(
        '1. kmfOn 5gf}6 ug'{xf];\\n'
        '2. kl/jt{g ljsNk 5gf}6 ug'{xf];\\n'
        '3. kl/jt{g ;'? ug'{xf];\\n'
        '4. glthf ;]e ug'{xf];\\n'
    )
    
    doc.add_paragraph(
        'o; ;kmn6j]o/n] k|]tL km06df n]lvPsf] 6]S;6nfO{ '
        'o'lgsk]8 g]kfnL 6]S;6df ;kmntfk"j{s kl/jt{g ub{5 .'
    )
    
    # Save the document
    doc.save('test_files/sample.docx')
    print("Created test_files/sample.docx")

def create_pdf_file():
    """Create a sample PDF file with Preeti text."""
    filename = 'test_files/sample.pdf'
    c = canvas.Canvas(filename, pagesize=letter)
    width, height = letter
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "g]kfnL efiff k|]tL km06 6]S;6")
    
    # Content
    c.setFont("Helvetica", 12)
    y_position = height - 100
    
    lines = [
        "of] Pp6f g]kfnL efiff k|]tL km06df n]lvPsf] b:tfj]h xf] .",
        "",
        "ljz]iftfx?:",
        "• PDF kmfOnx? kl/jt{g",
        "• Word b:tfj]hx? kl/jt{g", 
        "• ;fdfGo 6]S;6 kmfOnx? kl/jt{g",
        "• Batch k|f];];Ë ;'ljwf",
        "",
        "k|of]u ljlw:",
        "1. kmfOn 5gf}6 ug'{xf];\\",
        "2. kl/jt{g ljsNk 5gf}6 ug'{xf];\\",
        "3. kl/jt{g ;'? ug'{xf];\\",
        "4. glthf ;]e ug'{xf];\\",
        "",
        "o; ;kmn6j]o/n] k|]tL km06df n]lvPsf] 6]S;6nfO{",
        "o'lgsk]8 g]kfnL 6]S;6df ;kmntfk"j{s kl/jt{g ub{5 .",
        "",
        "Email: <EMAIL>",
        "Website: https://github.com/diwaskunwar/preeti-unicode-converter"
    ]
    
    for line in lines:
        c.drawString(50, y_position, line)
        y_position -= 20
        if y_position < 50:  # Start new page if needed
            c.showPage()
            c.setFont("Helvetica", 12)
            y_position = height - 50
    
    c.save()
    print("Created test_files/sample.pdf")

def main():
    """Create all test files."""
    create_test_directory()
    create_docx_file()
    create_pdf_file()
    print("All test files created successfully!")

if __name__ == "__main__":
    main()

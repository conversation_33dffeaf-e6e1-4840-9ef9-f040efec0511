[project]
name = "preeti-unicode-converter"
version = "0.1.0"
description = "A highly customizable Python package for converting Preeti font text to Unicode (Nepali)"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON>nwar", email = "<EMAIL>" }
]
maintainers = [
    { name = "<PERSON>was Kunwar", email = "<EMAIL>" }
]
license = { text = "MIT" }
requires-python = ">=3.8.1"
dependencies = [
    "pymupdf>=1.23.0",
    "python-docx>=1.1.0",
    "reportlab>=4.0.0",
]
keywords = ["preeti", "unicode", "nepali", "font", "conversion", "devanagari"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Text Processing",
    "Topic :: Text Processing :: Fonts",
    "Topic :: Utilities",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Natural Language :: Nepali",
]

[project.urls]
Homepage = "https://github.com/diwaskunwar/preeti-unicode-converter"
Repository = "https://github.com/diwaskunwar/preeti-unicode-converter"
Issues = "https://github.com/diwaskunwar/preeti-unicode-converter/issues"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
preeti-unicode = "preeti_unicode.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/preeti_unicode"]

[tool.hatch.build.targets.sdist]
include = [
    "/src",
    "/README.md",
    "/LICENSE",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "--cov=preeti_unicode --cov-report=html --cov-report=term-missing"

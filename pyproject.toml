[project]
name = "preeti-unicode-converter"
version = "0.1.0"
description = "A highly customizable Python package for converting Preeti font text to Unicode (Nepali)"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON>nwar", email = "<EMAIL>" }
]
maintainers = [
    { name = "<PERSON>was Kunwar", email = "<EMAIL>" }
]
license = { text = "MIT" }
requires-python = ">=3.8.1"
dependencies = [
    "pymupdf>=1.23.0",
    "python-docx>=1.1.0",
    "reportlab>=4.0.0",
]
keywords = ["preeti", "unicode", "nepali", "font", "conversion", "devanagari"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Text Processing",
    "Topic :: Text Processing :: Fonts",
    "Topic :: Utilities",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Natural Language :: Nepali",
]

[project.urls]
Homepage = "https://github.com/diwaskunwar/preeti-unicode"
Repository = "https://github.com/diwaskunwar/preeti-unicode"
Issues = "https://github.com/diwaskunwar/preeti-unicode/issues"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.scripts]
preeti-unicode = "preeti_unicode.cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/preeti_unicode"]

"""Tests for the enhanced Preeti Unicode converter."""

import asyncio
import pytest
from pathlib import Path
import tempfile
import shutil

from preeti_unicode.enhanced_converter import EnhancedPreetiConverter


class TestEnhancedPreetiConverter:
    """Test cases for EnhancedPreetiConverter."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.converter = EnhancedPreetiConverter(show_progress=False, colorful_logs=False)
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """Clean up test fixtures."""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def test_converter_initialization(self):
        """Test that enhanced converter can be initialized."""
        converter = EnhancedPreetiConverter()
        assert converter is not None
        assert hasattr(converter, 'base_converter')
    
    def test_sync_text_conversion(self):
        """Test synchronous text conversion."""
        test_text = "g]kfn"
        result = self.converter.convert_text(test_text)
        assert result is not None
        assert isinstance(result, str)
    
    @pytest.mark.asyncio
    async def test_async_text_conversion(self):
        """Test asynchronous text conversion."""
        test_text = "g]kfn"
        result = await self.converter.convert_text_async(test_text)
        assert result is not None
        assert isinstance(result, str)
    
    def test_create_test_files(self):
        """Test creating test files in user directory."""
        test_dir = self.converter.create_test_files(self.temp_dir)
        
        assert test_dir.exists()
        assert test_dir.is_dir()
        
        # Check that sample files were created
        sample_files = list(test_dir.glob("*.txt"))
        assert len(sample_files) >= 1
        
        # Check content of first sample file
        sample_file = test_dir / "sample.txt"
        assert sample_file.exists()
        
        content = sample_file.read_text(encoding='utf-8')
        assert "g]kfnL efiff" in content
        assert "k|]tL km06" in content
    
    def test_sync_file_conversion(self):
        """Test synchronous file conversion."""
        # Create test files first
        test_dir = self.converter.create_test_files(self.temp_dir)
        sample_file = test_dir / "sample.txt"
        
        # Convert the file
        output_path = self.converter.convert_file(
            sample_file, 
            output_dir=self.temp_dir / "output"
        )
        
        assert output_path.exists()
        assert output_path.suffix == ".txt"
        assert "unicode" in output_path.name
        
        # Check that output file has content
        content = output_path.read_text(encoding='utf-8')
        assert len(content) > 0
    
    @pytest.mark.asyncio
    async def test_async_file_conversion(self):
        """Test asynchronous file conversion."""
        # Create test files first
        test_dir = self.converter.create_test_files(self.temp_dir)
        sample_file = test_dir / "sample.txt"
        
        # Convert the file
        output_path = await self.converter.convert_file_async(
            sample_file, 
            output_dir=self.temp_dir / "output"
        )
        
        assert output_path.exists()
        assert output_path.suffix == ".txt"
        assert "unicode" in output_path.name
        
        # Check that output file has content
        content = output_path.read_text(encoding='utf-8')
        assert len(content) > 0
    
    def test_sync_multiple_files_conversion(self):
        """Test synchronous multiple files conversion."""
        # Create test files first
        test_dir = self.converter.create_test_files(self.temp_dir)
        input_files = list(test_dir.glob("*.txt"))
        
        # Convert multiple files
        output_paths = self.converter.convert_multiple_files(
            input_files,
            output_dir=self.temp_dir / "batch_output"
        )
        
        assert len(output_paths) >= 1
        for output_path in output_paths:
            assert output_path.exists()
            assert "unicode" in output_path.name
    
    @pytest.mark.asyncio
    async def test_async_multiple_files_conversion(self):
        """Test asynchronous multiple files conversion."""
        # Create test files first
        test_dir = self.converter.create_test_files(self.temp_dir)
        input_files = list(test_dir.glob("*.txt"))
        
        # Convert multiple files
        output_paths = await self.converter.convert_multiple_files_async(
            input_files,
            output_dir=self.temp_dir / "async_batch_output",
            max_concurrent=2
        )
        
        assert len(output_paths) >= 1
        for output_path in output_paths:
            assert output_path.exists()
            assert "unicode" in output_path.name
    
    def test_progress_tracking_initialization(self):
        """Test converter with progress tracking enabled."""
        converter_with_progress = EnhancedPreetiConverter(
            show_progress=True, 
            colorful_logs=True
        )
        assert converter_with_progress is not None
    
    def test_file_conversion_to_current_directory(self):
        """Test that files are saved to current directory when no output_dir specified."""
        # Create test files first
        test_dir = self.converter.create_test_files(self.temp_dir)
        sample_file = test_dir / "sample.txt"
        
        # Change to temp directory and convert
        import os
        original_cwd = os.getcwd()
        try:
            os.chdir(self.temp_dir)
            output_path = self.converter.convert_file(sample_file)
            
            # Should be in current directory (temp_dir)
            assert output_path.parent == self.temp_dir
            assert output_path.exists()
            
        finally:
            os.chdir(original_cwd)

"""Basic tests for the preeti unicode converter."""

import pytest
from preeti_unicode.converter import PreetiUnicodeConverter
from preeti_unicode.enhanced_converter import EnhancedPreetiConverter


class TestPreetiUnicodeConverter:
    """Test cases for PreetiUnicodeConverter."""

    def test_converter_initialization(self):
        """Test that converter can be initialized."""
        converter = PreetiUnicodeConverter()
        assert converter is not None

    def test_basic_conversion(self):
        """Test basic text conversion functionality."""
        converter = PreetiUnicodeConverter()

        # Test that the method exists and works
        assert hasattr(converter, 'convert')

        # Test with sample text
        test_text = "g]kfn"
        result = converter.convert(test_text)
        assert isinstance(result, str)
        assert len(result) > 0

    def test_empty_string_conversion(self):
        """Test conversion of empty string."""
        converter = PreetiUnicodeConverter()
        result = converter.convert("")
        assert result == ""

    def test_none_input_handling(self):
        """Test handling of None input."""
        converter = PreetiUnicodeConverter()
        with pytest.raises((Type<PERSON>rror, ValueError, AttributeError)):
            converter.convert(None)


class TestEnhancedPreetiConverter:
    """Test cases for EnhancedPreetiConverter."""

    def test_enhanced_converter_initialization(self):
        """Test that enhanced converter can be initialized."""
        converter = EnhancedPreetiConverter()
        assert converter is not None
        assert hasattr(converter, 'base_converter')

    def test_enhanced_text_conversion(self):
        """Test enhanced text conversion."""
        converter = EnhancedPreetiConverter(show_progress=False, colorful_logs=False)

        test_text = "g]kfn"
        result = converter.convert_text(test_text)
        assert isinstance(result, str)
        assert len(result) > 0

    def test_create_test_files_functionality(self):
        """Test that create_test_files method works."""
        converter = EnhancedPreetiConverter(show_progress=False, colorful_logs=False)

        # This should not raise an exception
        test_dir = converter.create_test_files()
        assert test_dir is not None

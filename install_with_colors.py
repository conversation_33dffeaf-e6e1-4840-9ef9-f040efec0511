#!/usr/bin/env python3
"""
Colorful installation script for the Enhanced Preeti Unicode Converter.

This script installs the package with colorful output and demonstrates the installation process.
"""

import subprocess
import sys
from pathlib import Path

try:
    from colorama import init, Fore, Style, Back
    init()
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False


def print_colored(message, color=None, style=None, bg=None):
    """Print colored message if colorama is available."""
    if COLORAMA_AVAILABLE and color:
        prefix = ""
        suffix = Style.RESET_ALL
        
        if color:
            prefix += color
        if style:
            prefix += style
        if bg:
            prefix += bg
            
        print(f"{prefix}{message}{suffix}")
    else:
        print(message)


def print_banner():
    """Print installation banner."""
    if COLORAMA_AVAILABLE:
        print_colored("=" * 70, Fore.CYAN, Style.BRIGHT)
        print_colored("🚀 Enhanced Preeti Unicode Converter Installation", Fore.YELLOW, Style.BRIGHT)
        print_colored("=" * 70, Fore.CYAN, Style.BRIGHT)
        print()
        print_colored("📦 Installing with colorful progress indicators...", Fore.GREEN)
        print_colored("🎨 Features: Async/Sync • Progress Bars • Batch Processing", Fore.BLUE)
        print()
    else:
        print("=" * 70)
        print("🚀 Enhanced Preeti Unicode Converter Installation")
        print("=" * 70)
        print()
        print("📦 Installing with progress indicators...")
        print("🎨 Features: Async/Sync • Progress Bars • Batch Processing")
        print()


def run_command(command, description):
    """Run a command with colorful output."""
    print_colored(f"🔄 {description}...", Fore.YELLOW)
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            check=True
        )
        
        print_colored(f"✅ {description} completed successfully!", Fore.GREEN)
        
        # Print output if there's any
        if result.stdout.strip():
            print_colored("📋 Output:", Fore.CYAN)
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    print_colored(f"   {line}", Fore.WHITE)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print_colored(f"❌ {description} failed!", Fore.RED, Style.BRIGHT)
        print_colored(f"Error: {e.stderr}", Fore.RED)
        return False


def install_dependencies():
    """Install required dependencies."""
    dependencies = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install tqdm", "Installing tqdm (progress bars)"),
        ("pip install aiofiles", "Installing aiofiles (async file operations)"),
        ("pip install colorama", "Installing colorama (colorful output)"),
        ("pip install pytest", "Installing pytest (testing framework)"),
    ]
    
    print_colored("📚 Installing dependencies...", Fore.MAGENTA, Style.BRIGHT)
    print()
    
    for command, description in dependencies:
        if not run_command(command, description):
            print_colored(f"⚠️  Warning: {description} failed, but continuing...", Fore.YELLOW)
        print()


def install_package():
    """Install the package in development mode."""
    print_colored("📦 Installing Enhanced Preeti Unicode Converter...", Fore.MAGENTA, Style.BRIGHT)
    print()
    
    # Install in development mode
    success = run_command("pip install -e .", "Installing package in development mode")
    
    if success:
        print()
        print_colored("🎉 Installation completed successfully!", Fore.GREEN, Style.BRIGHT)
        print()
        print_colored("Available commands:", Fore.CYAN, Style.BRIGHT)
        print_colored("  preeti-unicode          - Basic CLI", Fore.WHITE)
        print_colored("  preeti-unicode-enhanced - Enhanced CLI with async/progress", Fore.WHITE)
        print()
        print_colored("Python usage:", Fore.CYAN, Style.BRIGHT)
        print_colored("  from preeti_unicode import EnhancedPreetiConverter", Fore.WHITE)
        print_colored("  converter = EnhancedPreetiConverter(show_progress=True)", Fore.WHITE)
        print()
    else:
        print_colored("❌ Package installation failed!", Fore.RED, Style.BRIGHT)
        return False
    
    return True


def run_demo():
    """Ask user if they want to run the demo."""
    print_colored("🎮 Would you like to run the demo? (y/n): ", Fore.CYAN, end="")
    
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            print()
            print_colored("🚀 Running demo...", Fore.GREEN, Style.BRIGHT)
            print()
            
            # Try to run the demo
            demo_success = run_command("python example_usage.py", "Running demo")
            
            if demo_success:
                print_colored("✨ Demo completed!", Fore.GREEN, Style.BRIGHT)
            else:
                print_colored("⚠️  Demo failed, but package should still work", Fore.YELLOW)
        else:
            print_colored("👍 Skipping demo. You can run it later with: python example_usage.py", Fore.BLUE)
    
    except KeyboardInterrupt:
        print()
        print_colored("👋 Demo skipped.", Fore.YELLOW)


def main():
    """Main installation function."""
    print_banner()
    
    # Check if we're in the right directory
    if not Path("pyproject.toml").exists():
        print_colored("❌ Error: pyproject.toml not found!", Fore.RED, Style.BRIGHT)
        print_colored("Please run this script from the project root directory.", Fore.RED)
        sys.exit(1)
    
    try:
        # Install dependencies
        install_dependencies()
        
        # Install the package
        if install_package():
            # Offer to run demo
            run_demo()
            
            print()
            print_colored("🎊 All done! Happy converting! 🎊", Fore.GREEN, Style.BRIGHT)
            print_colored("📖 Check example_usage.py for usage examples", Fore.BLUE)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print()
        print_colored("❌ Installation cancelled by user", Fore.RED)
        sys.exit(1)
    except Exception as e:
        print_colored(f"❌ Unexpected error: {e}", Fore.RED, Style.BRIGHT)
        sys.exit(1)


if __name__ == "__main__":
    main()

name: release

on:
  push:
    tags:
      - "[0-9]+.[0-9]+.[0-9]+"
      - "[0-9]+.[0-9]+.[0-9]+a[0-9]+"
      - "[0-9]+.[0-9]+.[0-9]+b[0-9]+"
      - "[0-9]+.[0-9]+.[0-9]+rc[0-9]+"

env:
  PACKAGE_NAME: "preeti-unicode-converter"

jobs:
  details:
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ steps.release.outputs.new_version }}
      suffix: ${{ steps.release.outputs.suffix }}
      tag_name: ${{ steps.release.outputs.tag_name }}
    steps:
      - uses: actions/checkout@v4
      - name: Extract tag and Details
        id: release
        run: |
          if [ "${{ github.ref_type }}" = "tag" ]; then
            TAG_NAME=${GITHUB_REF#refs/tags/}
            NEW_VERSION=$(echo $TAG_NAME | awk -F'-' '{print $1}')
            SUFFIX=$(echo $TAG_NAME | grep -oP '[a-z]+[0-9]+' || echo "")
            echo "new_version=$NEW_VERSION" >> "$GITHUB_OUTPUT"
            echo "suffix=$SUFFIX" >> "$GITHUB_OUTPUT"
            echo "tag_name=$TAG_NAME" >> "$GITHUB_OUTPUT"
            echo "Version is $NEW_VERSION"
            echo "Suffix is $SUFFIX"
            echo "Tag name is $TAG_NAME"
          else
            echo "No tag found"
            exit 1
          fi

  check_pypi:
    needs: details
    runs-on: ubuntu-latest
    steps:
      - name: Fetch information from PyPI
        run: |
          response=$(curl -s https://pypi.org/pypi/${{ env.PACKAGE_NAME }}/json || echo "{}")
          latest_previous_version=$(echo $response | jq --raw-output "select(.releases != null) | .releases | keys_unsorted | last")
          if [ -z "$latest_previous_version" ]; then
            echo "Package not found on PyPI."
            latest_previous_version="0.0.0"
          fi
          echo "Latest version on PyPI: $latest_previous_version"
          echo "latest_previous_version=$latest_previous_version" >> $GITHUB_ENV
      - name: Compare versions and exit if not newer
        run: |
          NEW_VERSION=${{ needs.details.outputs.new_version }}
          LATEST_VERSION=$latest_previous_version
          if [ "$(printf '%s\n' "$LATEST_VERSION" "$NEW_VERSION" | sort -rV | head -n 1)" != "$NEW_VERSION" ] || [ "$NEW_VERSION" == "$LATEST_VERSION" ]; then
            echo "The new version $NEW_VERSION is not greater than the latest version $LATEST_VERSION on PyPI."
            exit 1
          else
            echo "The new version $NEW_VERSION is greater than the latest version $LATEST_VERSION on PyPI."
          fi

  test:
    needs: [details, check_pypi]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Run tests
      run: |
        pytest

    - name: Run linting
      run: |
        black --check src/
        isort --check-only src/
        flake8 src/

  setup_and_build:
    needs: [details, check_pypi, test]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12"
      - name: Install build dependencies
        run: |
          python -m pip install --upgrade pip
          pip install build hatchling
      - name: Update version in pyproject.toml
        run: |
          sed -i 's/version = ".*"/version = "${{ needs.details.outputs.new_version }}"/' pyproject.toml
      - name: Build source and wheel distribution
        run: |
          python -m build
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/

  pypi_publish:
    name: Upload release to PyPI
    needs: [setup_and_build, details]
    runs-on: ubuntu-latest
    environment:
      name: pypi
    permissions:
      id-token: write
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist/
      - name: Publish distribution to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}

  github_release:
    name: Create GitHub Release
    needs: [setup_and_build, details]
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist/
      - name: Create GitHub Release
        id: create_release
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          gh release create ${{ needs.details.outputs.tag_name }} dist/* --title ${{ needs.details.outputs.tag_name }} --generate-notes

"""Enhanced Preeti Unicode Converter with async/sync support and progress tracking."""

import asyncio
import os
from pathlib import Path
from typing import List, Union, Optional, Dict, Any
import logging

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
except ImportError:
    AIOFILES_AVAILABLE = False

try:
    from colorama import init, Fore, Style
    init()  # Initialize colorama
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False

from .converter import PreetiUnicodeConverter


class EnhancedPreetiConverter:
    """Enhanced converter with async/sync support and progress tracking."""
    
    def __init__(self, show_progress: bool = True, colorful_logs: bool = True):
        """
        Initialize the enhanced converter.
        
        Args:
            show_progress: Whether to show progress bars (requires tqdm)
            colorful_logs: Whether to use colorful logging (requires colorama)
        """
        self.base_converter = PreetiUnicodeConverter()
        self.show_progress = show_progress and TQDM_AVAILABLE
        self.colorful_logs = colorful_logs and COLORAMA_AVAILABLE
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup colorful logging."""
        self.logger = logging.getLogger(__name__)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            if self.colorful_logs:
                formatter = logging.Formatter(
                    f'{Fore.CYAN}%(asctime)s{Style.RESET_ALL} - '
                    f'{Fore.GREEN}%(name)s{Style.RESET_ALL} - '
                    f'{Fore.YELLOW}%(levelname)s{Style.RESET_ALL} - '
                    f'%(message)s'
                )
            else:
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def _log_info(self, message: str):
        """Log info message with optional color."""
        if self.colorful_logs:
            self.logger.info(f"{Fore.GREEN}✅ {message}{Style.RESET_ALL}")
        else:
            self.logger.info(f"✅ {message}")
    
    def _log_error(self, message: str):
        """Log error message with optional color."""
        if self.colorful_logs:
            self.logger.error(f"{Fore.RED}❌ {message}{Style.RESET_ALL}")
        else:
            self.logger.error(f"❌ {message}")
    
    def _log_warning(self, message: str):
        """Log warning message with optional color."""
        if self.colorful_logs:
            self.logger.warning(f"{Fore.YELLOW}⚠️ {message}{Style.RESET_ALL}")
        else:
            self.logger.warning(f"⚠️ {message}")
    
    def convert_text(self, text: str) -> str:
        """
        Convert Preeti text to Unicode (sync version).
        
        Args:
            text: Preeti text to convert
            
        Returns:
            Converted Unicode text
        """
        return self.base_converter.convert(text)
    
    async def convert_text_async(self, text: str) -> str:
        """
        Convert Preeti text to Unicode (async version).
        
        Args:
            text: Preeti text to convert
            
        Returns:
            Converted Unicode text
        """
        # Run the conversion in a thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.base_converter.convert, text)
    
    def convert_file(self, 
                    input_path: Union[str, Path], 
                    output_path: Optional[Union[str, Path]] = None,
                    output_dir: Optional[Union[str, Path]] = None) -> Path:
        """
        Convert a single file (sync version).
        
        Args:
            input_path: Path to input file
            output_path: Path to output file (optional)
            output_dir: Directory to save output file (optional, defaults to current dir)
            
        Returns:
            Path to the converted file
        """
        input_path = Path(input_path)
        
        if output_path is None:
            if output_dir is None:
                output_dir = Path.cwd()
            else:
                output_dir = Path(output_dir)
            output_dir.mkdir(exist_ok=True)
            output_path = output_dir / f"{input_path.stem}_unicode{input_path.suffix}"
        else:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
        
        self._log_info(f"Converting file: {input_path}")
        
        try:
            # Read input file
            with open(input_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Convert content
            converted_content = self.convert_text(content)
            
            # Write output file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(converted_content)
            
            self._log_info(f"File converted successfully: {output_path}")
            return output_path
            
        except Exception as e:
            self._log_error(f"Error converting file {input_path}: {str(e)}")
            raise
    
    async def convert_file_async(self, 
                               input_path: Union[str, Path], 
                               output_path: Optional[Union[str, Path]] = None,
                               output_dir: Optional[Union[str, Path]] = None) -> Path:
        """
        Convert a single file (async version).
        
        Args:
            input_path: Path to input file
            output_path: Path to output file (optional)
            output_dir: Directory to save output file (optional, defaults to current dir)
            
        Returns:
            Path to the converted file
        """
        if not AIOFILES_AVAILABLE:
            # Fallback to sync version if aiofiles not available
            return self.convert_file(input_path, output_path, output_dir)
        
        input_path = Path(input_path)
        
        if output_path is None:
            if output_dir is None:
                output_dir = Path.cwd()
            else:
                output_dir = Path(output_dir)
            output_dir.mkdir(exist_ok=True)
            output_path = output_dir / f"{input_path.stem}_unicode{input_path.suffix}"
        else:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
        
        self._log_info(f"Converting file: {input_path}")
        
        try:
            # Read input file asynchronously
            async with aiofiles.open(input_path, 'r', encoding='utf-8') as f:
                content = await f.read()
            
            # Convert content
            converted_content = await self.convert_text_async(content)
            
            # Write output file asynchronously
            async with aiofiles.open(output_path, 'w', encoding='utf-8') as f:
                await f.write(converted_content)
            
            self._log_info(f"File converted successfully: {output_path}")
            return output_path
            
        except Exception as e:
            self._log_error(f"Error converting file {input_path}: {str(e)}")
            raise

    def convert_multiple_files(self,
                             input_paths: List[Union[str, Path]],
                             output_dir: Optional[Union[str, Path]] = None) -> List[Path]:
        """
        Convert multiple files (sync version).

        Args:
            input_paths: List of input file paths
            output_dir: Directory to save output files (optional, defaults to current dir)

        Returns:
            List of paths to converted files
        """
        if output_dir is None:
            output_dir = Path.cwd()
        else:
            output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)

        converted_files = []

        # Setup progress bar if available
        if self.show_progress:
            pbar = tqdm(input_paths, desc="Converting files", unit="file")
        else:
            pbar = input_paths

        for input_path in pbar:
            try:
                output_path = self.convert_file(input_path, output_dir=output_dir)
                converted_files.append(output_path)

                if self.show_progress:
                    pbar.set_postfix({"Current": Path(input_path).name})

            except Exception as e:
                self._log_error(f"Failed to convert {input_path}: {str(e)}")
                continue

        self._log_info(f"Batch conversion completed. {len(converted_files)}/{len(input_paths)} files converted successfully.")
        return converted_files

    async def convert_multiple_files_async(self,
                                         input_paths: List[Union[str, Path]],
                                         output_dir: Optional[Union[str, Path]] = None,
                                         max_concurrent: int = 5) -> List[Path]:
        """
        Convert multiple files (async version).

        Args:
            input_paths: List of input file paths
            output_dir: Directory to save output files (optional, defaults to current dir)
            max_concurrent: Maximum number of concurrent conversions

        Returns:
            List of paths to converted files
        """
        if output_dir is None:
            output_dir = Path.cwd()
        else:
            output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)

        semaphore = asyncio.Semaphore(max_concurrent)
        converted_files = []

        async def convert_with_semaphore(input_path):
            async with semaphore:
                try:
                    return await self.convert_file_async(input_path, output_dir=output_dir)
                except Exception as e:
                    self._log_error(f"Failed to convert {input_path}: {str(e)}")
                    return None

        # Setup progress bar if available
        if self.show_progress:
            pbar = tqdm(total=len(input_paths), desc="Converting files", unit="file")

        # Create tasks for all files
        tasks = [convert_with_semaphore(path) for path in input_paths]

        # Process tasks and update progress
        for coro in asyncio.as_completed(tasks):
            result = await coro
            if result is not None:
                converted_files.append(result)

            if self.show_progress:
                pbar.update(1)

        if self.show_progress:
            pbar.close()

        self._log_info(f"Batch conversion completed. {len(converted_files)}/{len(input_paths)} files converted successfully.")
        return converted_files

    def create_test_files(self, output_dir: Optional[Union[str, Path]] = None) -> Path:
        """
        Create test files in the specified directory.

        Args:
            output_dir: Directory to create test files (defaults to current directory)

        Returns:
            Path to the test files directory
        """
        if output_dir is None:
            output_dir = Path.cwd()
        else:
            output_dir = Path(output_dir)

        test_dir = output_dir / "test_files"
        test_dir.mkdir(exist_ok=True)

        # Create sample text file
        sample_content = """g]kfnL efiff k|]tL km06 6]S;6
============================

of] Pp6f g]kfnL efiff k|]tL km06df n]lvPsf] b:tfj]h xf] .

ljz]iftfx?M
• PDF kmfOnx? kl/jt{g
• Word b:tfj]hx? kl/jt{g
• ;fdfGo 6]S;6 kmfOnx? kl/jt{g

k|of]u ljlwM
1. kmfOn 5gf}6 ug'{xf];
2. kl/jt{g ljsNk 5gf}6 ug'{xf];
3. kl/jt{g ;'? ug'{xf];
4. kl/jt{g ;lsPkl5 glthf ;]e ug'{xf];

;Dks{M
Email: <EMAIL>
Website: https://github.com/diwaskunwar/preeti-unicode-converter"""

        sample_file = test_dir / "sample.txt"
        sample_file.write_text(sample_content, encoding='utf-8')

        # Create second sample file
        sample2_content = """k|]tL km06 6]S;6 gd'gf @
========================

of] bf];|f] gd'gf kmfOn xf] .

;fdfGo zAbx?M
3/, ;do, sfd, gfd, 3/df, ;dodf, sfddf

jfSox?M
d]/f] gfd /fd xf] .
p;sf] 3/ sf7df8f}+df 5 .
xfdL g]kfnL efiff af]N5f}+ ."""

        sample2_file = test_dir / "sample2.txt"
        sample2_file.write_text(sample2_content, encoding='utf-8')

        self._log_info(f"Test files created in: {test_dir}")
        self._log_info(f"Created files: {[f.name for f in test_dir.glob('*.txt')]}")

        return test_dir

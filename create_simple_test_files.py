#!/usr/bin/env python3
"""Create simple test files without external dependencies."""

import os
from pathlib import Path

def create_test_files_in_user_dir(output_dir=None):
    """Create test files in user's current directory or specified directory."""
    if output_dir is None:
        output_dir = Path.cwd()
    else:
        output_dir = Path(output_dir)
    
    # Create test_files directory
    test_dir = output_dir / "test_files"
    test_dir.mkdir(exist_ok=True)
    
    # Create TXT file
    txt_content = """g]kfnL efiff k|]tL km06 6]S;6
============================

of] Pp6f g]kfnL efiff k|]tL km06df n]lvPsf] b:tfj]h xf] . o;df ljleGg k|sf/sf g]kfnL zAbx? / jfSox? 5g\\ .

ljz]iftfx?M
===========
• PDF kmfOnx? kl/jt{g
• Word b:tfj]hx? kl/jt{g  
• ;fdfGo 6]S;6 kmfOnx? kl/jt{g
• Batch k|f];];Ë ;'ljwf
• pRr u'0f:t/Lo kl/jt{g

k|of]u ljlwM
============
1. kmfOn 5gf}6 ug'{xf];\ 
2. kl/jt{g ljsNk 5gf}6 ug'{xf];\
3. kl/jt{g ;'? ug'{xf];\
4. kl/jt{g ;lsPkl5 glthf ;]e ug'{xf];\

pbfx/0f zAbx?M
==============
g]kfn, sf7df8f}+, kf]v/f, lrtjg, w/fg, e}/xjf, hgsk'/, a'6jn
lzIff, :jf:Yo, s[lif, ko{6g, ;+:s[lt, Oltxf;, /fhgLlt
k|]d, ;Ddfg, ;]jf, ;xof]u, ;xsfo{, ;fd'bflos, ;fdflhs

;Dks{M
======
Email: <EMAIL>
Website: https://github.com/diwaskunwar/preeti-unicode-converter
Phone: +977-1-4444444

wGojfbÙ"""
    
    txt_file = test_dir / "sample.txt"
    txt_file.write_text(txt_content, encoding='utf-8')
    print(f"✅ Created: {txt_file}")
    
    # Create another TXT file for batch testing
    txt2_content = """k|]tL km06 6]S;6 gd'gf @
========================

of] bf];|f] gd'gf kmfOn xf] . o;df klg k|]tL km06sf zAbx? 5g\\ .

;fdfGo zAbx?M
=============
3/, 3/df, 3/af6, 3/lt/, 3/sf], 3/df+
;do, ;dodf, ;doaf6, ;dolt/, ;dosf], ;dodf+
sfd, sfddf, sfdaf6, sfdlt/, sfdsf], sfddf+

jfSox?M
=======
d]/f] gfd /fd xf] .
p;sf] 3/ sf7df8f}+df 5 .
xfdL g]kfnL efiff af]N5f}+ .
ltdL s] sfd ub{5f} <
pgLx? lbgel/ sfd ub{5g\\ .

;dfkg
======
o; gd'gf kmfOndf k|]tL km06sf ljleGg zAbx? / jfSox? 5g\\ ."""
    
    txt2_file = test_dir / "sample2.txt"
    txt2_file.write_text(txt2_content, encoding='utf-8')
    print(f"✅ Created: {txt2_file}")
    
    # Create a simple CSV-like file for batch processing
    csv_content = """filename,preeti_text,expected_unicode
file1.txt,"g]kfn","नेपाल"
file2.txt,"sf7df8f}+","काठमाडौं"
file3.txt,"kf]v/f","पोखरा"
file4.txt,"lrtjg","चितवन"
file5.txt,"w/fg","धरान"
"""
    
    csv_file = test_dir / "batch_test.csv"
    csv_file.write_text(csv_content, encoding='utf-8')
    print(f"✅ Created: {csv_file}")
    
    return test_dir

if __name__ == "__main__":
    create_test_files_in_user_dir()
    print("All test files created successfully!")

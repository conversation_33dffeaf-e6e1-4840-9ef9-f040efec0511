#!/usr/bin/env python3
"""
Example usage of the Enhanced Preeti Unicode Converter.

This script demonstrates all the features:
- Sync/Async text conversion
- Single file conversion
- Multiple file conversion with progress bars
- Test file creation in user's directory
- Colorful logging
"""

import asyncio
from pathlib import Path

# Import the enhanced converter
from src.preeti_unicode.enhanced_converter import EnhancedPreetiConverter


async def demonstrate_features():
    """Demonstrate all features of the enhanced converter."""
    
    print("🚀 Enhanced Preeti Unicode Converter Demo")
    print("=" * 50)
    
    # Initialize converter with progress bars and colorful logs
    converter = EnhancedPreetiConverter(
        show_progress=True,
        colorful_logs=True
    )
    
    # 1. Create test files in current directory
    print("\n📁 Creating test files...")
    test_dir = converter.create_test_files()
    print(f"Test files created in: {test_dir}")
    
    # 2. Demonstrate text conversion (sync)
    print("\n📝 Text Conversion (Sync):")
    test_texts = ["g]kfn", "sf7df8f}+", "kf]v/f", "lrtjg"]
    for text in test_texts:
        converted = converter.convert_text(text)
        print(f"  {text} → {converted}")
    
    # 3. Demonstrate text conversion (async)
    print("\n📝 Text Conversion (Async):")
    for text in test_texts:
        converted = await converter.convert_text_async(text)
        print(f"  {text} → {converted}")
    
    # 4. Single file conversion (sync)
    print("\n📄 Single File Conversion (Sync):")
    sample_file = test_dir / "sample.txt"
    if sample_file.exists():
        output_path = converter.convert_file(
            sample_file,
            output_dir=Path.cwd() / "converted_files"
        )
        print(f"Converted: {sample_file} → {output_path}")
    
    # 5. Single file conversion (async)
    print("\n📄 Single File Conversion (Async):")
    sample2_file = test_dir / "sample2.txt"
    if sample2_file.exists():
        output_path = await converter.convert_file_async(
            sample2_file,
            output_dir=Path.cwd() / "converted_files_async"
        )
        print(f"Converted: {sample2_file} → {output_path}")
    
    # 6. Multiple files conversion (sync with progress)
    print("\n📚 Multiple Files Conversion (Sync with Progress):")
    input_files = list(test_dir.glob("*.txt"))
    if input_files:
        output_paths = converter.convert_multiple_files(
            input_files,
            output_dir=Path.cwd() / "batch_converted"
        )
        print(f"Batch converted {len(output_paths)} files")
    
    # 7. Multiple files conversion (async with progress)
    print("\n📚 Multiple Files Conversion (Async with Progress):")
    if input_files:
        output_paths = await converter.convert_multiple_files_async(
            input_files,
            output_dir=Path.cwd() / "batch_converted_async",
            max_concurrent=3
        )
        print(f"Async batch converted {len(output_paths)} files")
    
    print("\n✅ Demo completed! Check the created directories:")
    print("  - test_files/")
    print("  - converted_files/")
    print("  - converted_files_async/")
    print("  - batch_converted/")
    print("  - batch_converted_async/")


def demonstrate_sync_only():
    """Demonstrate sync-only features (for environments without async support)."""
    
    print("🚀 Enhanced Preeti Unicode Converter Demo (Sync Only)")
    print("=" * 55)
    
    # Initialize converter
    converter = EnhancedPreetiConverter(
        show_progress=True,
        colorful_logs=True
    )
    
    # Create test files
    print("\n📁 Creating test files...")
    test_dir = converter.create_test_files()
    
    # Text conversion
    print("\n📝 Text Conversion:")
    test_texts = ["g]kfn", "sf7df8f}+", "kf]v/f"]
    for text in test_texts:
        converted = converter.convert_text(text)
        print(f"  {text} → {converted}")
    
    # File conversion
    print("\n📄 File Conversion:")
    sample_file = test_dir / "sample.txt"
    if sample_file.exists():
        output_path = converter.convert_file(
            sample_file,
            output_dir=Path.cwd() / "sync_converted"
        )
        print(f"Converted: {output_path}")
    
    # Multiple files
    print("\n📚 Multiple Files Conversion:")
    input_files = list(test_dir.glob("*.txt"))
    if input_files:
        output_paths = converter.convert_multiple_files(
            input_files,
            output_dir=Path.cwd() / "sync_batch_converted"
        )
        print(f"Batch converted {len(output_paths)} files")
    
    print("\n✅ Sync demo completed!")


if __name__ == "__main__":
    print("Choose demo mode:")
    print("1. Full demo (with async features)")
    print("2. Sync-only demo")
    
    try:
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            asyncio.run(demonstrate_features())
        elif choice == "2":
            demonstrate_sync_only()
        else:
            print("Invalid choice. Running sync-only demo...")
            demonstrate_sync_only()
            
    except KeyboardInterrupt:
        print("\n❌ Demo cancelled by user")
    except Exception as e:
        print(f"❌ Error running demo: {e}")
        print("Falling back to sync-only demo...")
        demonstrate_sync_only()
